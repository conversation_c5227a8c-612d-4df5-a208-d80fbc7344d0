import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
// import SemiPlugin from "vite-plugin-semi-theme";

import {viteStaticCopy} from 'vite-plugin-static-copy';

import { resolve } from 'path'
// https://vitejs.dev/config/
export default defineConfig({
  base: '/mesh',
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    }
  },
  plugins: [
    react(),    
    viteStaticCopy({
      targets: [
        {
          src: 'node_modules/monaco-editor/min/vs',
          dest: 'public'
        }
      ]
    })
    // SemiPlugin({
    //   theme: "@semi-bot/semi-theme-feiyue-cloud"
    // }),
  ],
  server: {
    base: '/mesh',
    proxy: {
      '/api/': {
        "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
        "changeOrigin": true, // 允许域名进行转换
        "headers": { "Cookie": "Hm_lvt_991451d4e71853fb3e6dbeb77dc47473=1735392173,1735614576; Hm_lvt_942e5619ea4c0fd13d0531688f8a0e1f=1751638615,1753846873; dev_sandbox_feiyue_cloud_session_continuity=MTc1NjE3NDQ1M3xEdi1CQkFFQ180SUFBUkFCRUFBQUJQLUNBQUE9fI3-KEheDFg0ls3ZrJSLb5e2Wy1EIhMJlUOa-GC9QlMW; csrf_token_f9f405b5391baab9e94e5290179b20bbf404673ef36f6d656dfaa0e7513afa71=ULksHY/gHyWTCP9qawVIxD+BzdaT2P10KLgQ9zbNQmU=; dev_sandbox_feiyue_cloud_session=MTc1NjE3NDQ1NHxpeWxKalA5Y2drZFZCNmlfcndRSDkzcHVGVE9oTVFEcHJMNlUxWmZ6TmYxbHBzTEp0cW9rc0lhR3kwUVNnRTZJX3YtLXdBWl93TnFRbEp0dmRRZzlrbEk3aXlvNnFtYzlLOGpqZzFfdnJlTjZOekg0WXZRbG9CT1V3Y3NLRTE2RGQyRmFmemQ2SzNkWllhQjB4Zm9fc1ltRnNhZ1Z3N2poS002UzlIM1NNSlF4Zy1FLXpGVG91SWdDbmoyWXdzYThTRVZmQTBsam43UUVpYXN0YU8teHlIdGIwQmFucUE9PXy0CoB4rFL75Btis_rWb8LBnGgt3pivTdQmLeCfCSlyBQ==; dev_sandbox_feiyue_cloud_session=MTc1NjE3NDQ1NHxDY1htUDBUQllORl81d2U2cGhySGZXcmFFeDhOOE90SllmRHVQdDdmZUJDYm1zUDdqZjAzUm1qZTdzR3g1S1JxQ1NacEI2N0RIZmUwdlpyRzhjd0dPRElFeHlFcjE3RlJFRElzcmd6TDdiamliTG1XcVFkN0RHN3QyR3V4WEtFbm9WY1puYnQxTHNrSTg1NXNZLWJaTHZhb2VEUW0zZFQ2MzJKY3QxYW1GMDZjcXJJQ05EYjNPNEo3MUtNNktNTFY5Q1QxTHMxS0ZaTENHMkdBbjBsZ0l6Y3B5WkV3NHc9PXxP64OHdGbPxZEdmh3pVyIUVWILWRdBZdHNzQX3RxGWtg==" },
      },
      // '/api/': {
      //   "target": "http://localhost:8080/", // 服务端域名
      //   changeOrigin: true,
      //   secure: false, // Set to true if your API uses HTTPS
      //   rewrite: (path) => path.replace(/^\/api\/mesh/, ''),
      //   configure: (proxy, options) => {
      //     proxy.on('proxyReq', (proxyReq, req, res) => {
      //       proxyReq.setHeader('Authorization', `Bearer RXwrieAYFJyY_DbLlTElUcaBLUZdaIotRlC`)
      //       proxyReq.setHeader('Accept', 'application/json')
      //     })
      //   }
      // },
      '/auth/sessions/whoami': {
        "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
        "changeOrigin": true, // 允许域名进行转换
        "headers": { "Cookie": "Hm_lvt_991451d4e71853fb3e6dbeb77dc47473=1735392173,1735614576; Hm_lvt_942e5619ea4c0fd13d0531688f8a0e1f=1751638615,1753846873; dev_sandbox_feiyue_cloud_session_continuity=MTc1NjE3NDQ1M3xEdi1CQkFFQ180SUFBUkFCRUFBQUJQLUNBQUE9fI3-KEheDFg0ls3ZrJSLb5e2Wy1EIhMJlUOa-GC9QlMW; csrf_token_f9f405b5391baab9e94e5290179b20bbf404673ef36f6d656dfaa0e7513afa71=ULksHY/gHyWTCP9qawVIxD+BzdaT2P10KLgQ9zbNQmU=; dev_sandbox_feiyue_cloud_session=MTc1NjE3NDQ1NHxpeWxKalA5Y2drZFZCNmlfcndRSDkzcHVGVE9oTVFEcHJMNlUxWmZ6TmYxbHBzTEp0cW9rc0lhR3kwUVNnRTZJX3YtLXdBWl93TnFRbEp0dmRRZzlrbEk3aXlvNnFtYzlLOGpqZzFfdnJlTjZOekg0WXZRbG9CT1V3Y3NLRTE2RGQyRmFmemQ2SzNkWllhQjB4Zm9fc1ltRnNhZ1Z3N2poS002UzlIM1NNSlF4Zy1FLXpGVG91SWdDbmoyWXdzYThTRVZmQTBsam43UUVpYXN0YU8teHlIdGIwQmFucUE9PXy0CoB4rFL75Btis_rWb8LBnGgt3pivTdQmLeCfCSlyBQ==; dev_sandbox_feiyue_cloud_session=MTc1NjE3NDQ1NHxDY1htUDBUQllORl81d2U2cGhySGZXcmFFeDhOOE90SllmRHVQdDdmZUJDYm1zUDdqZjAzUm1qZTdzR3g1S1JxQ1NacEI2N0RIZmUwdlpyRzhjd0dPRElFeHlFcjE3RlJFRElzcmd6TDdiamliTG1XcVFkN0RHN3QyR3V4WEtFbm9WY1puYnQxTHNrSTg1NXNZLWJaTHZhb2VEUW0zZFQ2MzJKY3QxYW1GMDZjcXJJQ05EYjNPNEo3MUtNNktNTFY5Q1QxTHMxS0ZaTENHMkdBbjBsZ0l6Y3B5WkV3NHc9PXxP64OHdGbPxZEdmh3pVyIUVWILWRdBZdHNzQX3RxGWtg==" },
      },
      '/auth/self-service/logout/browser': {
        "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
        "changeOrigin": true, // 允许域名进行转换
      },
      '/auth/self-service/logout': {
        "target": "https://dev.sandbox.feiyue.cloud/", // 服务端域名
        "changeOrigin": true, // 允许域名进行转换
      },
    }
  }
})