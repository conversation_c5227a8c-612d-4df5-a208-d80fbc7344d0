import React from 'react'
import { Typography, Table, Row, Col, Space, Tag, Spin, BackTop, Banner, Layout, Input, Select } from '@douyinfe/semi-ui';
import { IconSearch } from '@douyinfe/semi-icons';
import useTable, { DeviceFilter } from './useTable';
import { Location, useLocation, useNavigate } from 'react-router-dom';
import qs from 'query-string';
import { useLocale } from '@/locales';
import InfiniteScroll from 'react-infinite-scroll-component';

import { RDPMachine } from "@buf/flylayer_api.bufbuild_es/flylayer/v1/machines_pb";

import TableEmpty from '@/components/table-empty';
import { getQueryParam } from '@/utils/query';
import { BASE_PATH } from '@/constants/router';
const { Title, Paragraph } = Typography;
const { Sider, Content } = Layout;

// 参数设置过滤参数
const getDeviceFilter = (location: Location): DeviceFilter => {
    const keywords: string = getQueryParam('keywords', location) as string;
    const osQuery = getQueryParam('os', location);
    const connectStatus: string = getQueryParam('connectStatus', location) as string;
    const group: string = getQueryParam('group', location) as string;
    const meshEnabled: string = getQueryParam('meshEnabled', location) as string;



    let os: string[] = [];
    if (osQuery && Array.isArray(osQuery)) {
        os = osQuery as string[];
    }
    if (osQuery && typeof osQuery == 'string') {
        os = [osQuery as string];
    }
    return {
        keywords: keywords || '',
        os: os,
        connectStatus: connectStatus == 'online' || connectStatus == 'offline' ? connectStatus : '',
        group: group || '',
        meshEnabled: meshEnabled == 'enable' || meshEnabled == 'disable' ? meshEnabled : ''
    }
}

const Index: React.FC = () => {
    const location = useLocation();
        const navigate = useNavigate();
        const { formatMessage } = useLocale();
    
    const initFilter: DeviceFilter = getDeviceFilter(location);

    // 过滤参数改变时跳转路由
    const doNavigate = (param: DeviceFilter) => {

        let query = '';
        if (param.keywords || param.os.length > 0 || param.connectStatus || param.group || param.meshEnabled) {
            query = qs.stringify(param, {
                skipEmptyString: true
            });
        }
        if (query) {
            navigate(`${BASE_PATH}/rdp?${query}`)
        } else {
            navigate(`${BASE_PATH}/rdp`)
        }
    }

    const listConnectStatus = [
        { value: '', label: formatMessage({ id: 'devices.all' }) },
        { value: 'online', label: formatMessage({ id: 'devices.table.online' }) },
        { value: 'offline', label: formatMessage({ id: 'devices.table.offline' }) }
    ];
    const listOs = [
        { value: 'macOS', label: 'macOS' },
        // { value: 'iOS', label: 'iOS' },
        { value: 'windows', label: 'Windows' },
        // { value: 'linux', label: 'Linux' },
        // { value: 'android', label: 'Android' },
    ];
    

    const {
        loading,
        devices,
        filterParam,
        setFilterParam,
        total,
        addPage,
        pageSize,
        handleSort,
        columns
    } = useTable(initFilter)


    const handleOsChange = (value: any) => {

        setFilterParam({ ...filterParam, os: value })
        doNavigate({ ...filterParam, os: value });
    }

    const handleConnectStatusChange = (value: any) => {
        setFilterParam({ ...filterParam, connectStatus: value })
        doNavigate({ ...filterParam, connectStatus: value });
    }

    const handleQueryChange = (value: string) => {
        setFilterParam({ ...filterParam, keywords: value })
        doNavigate({ ...filterParam, keywords: value });
    }
    return <>
        <div className='general-page'>
            <Row className='mb10'>
                <Col span={20}>
                    <Title heading={3}>{formatMessage({ id: 'rdp.title' })}</Title>
                </Col>
            </Row>
            <Paragraph type='tertiary' className='mb40'>{formatMessage({ id: 'rdp.description' })}</Paragraph>
            <Layout className='mb20 search-bar' >
                <Layout>
                    <Content className='pr10'>
                        <Input value={filterParam.keywords}
                            onChange={handleQueryChange}
                            style={{ width: '100%' }} prefix={<IconSearch />} showClear placeholder={formatMessage({ id: 'devices.searchPlaceholder' })}></Input>
                    </Content>
                    <Sider> <Space>

                    <Select style={{ width: 200 }}
                        optionList={listConnectStatus}
                        insetLabel={formatMessage({ id: 'devices.status' })}
                        onChange={handleConnectStatusChange}
                        value={filterParam.connectStatus}></Select>
                        <Select multiple
                            maxTagCount={1}
                            style={{ width: 200 }}
                            optionList={listOs}
                            insetLabel={formatMessage({ id: 'rdp.operatingSystem' })}
                            onChange={handleOsChange}
                            value={filterParam.os}></Select>
                    </Space></Sider>
                </Layout>

            </Layout>
            <Banner type='warning' className='mb20' description={formatMessage({ id: 'rdp.banner.description' })} />

        <div style={{ height: 20 }} className='mb10' >  {!loading && <Tag>  {formatMessage({ id: 'devices.totalCount' })} {total}</Tag>} </div>
        <InfiniteScroll
            dataLength={devices.length} //This is important field to render the next data
            next={addPage}
            hasMore={devices.length < total}
            loader={<div><Spin></Spin></div>}
            endMessage={
                <div style={{ textAlign: 'center', paddingTop: 16, paddingBottom: 16 }}>
                    {devices.length > pageSize && <Paragraph type='tertiary'>{formatMessage({ id: 'devices.endMessage' })}</Paragraph>}
                </div>
            }
        >
            <Table
                rowKey={(record?: RDPMachine) => record ? record.machine?.id + '' : ''}
                onChange={handleSort}
                empty={<TableEmpty loading={loading} />} loading={loading} columns={columns} dataSource={devices} pagination={false} />
        </InfiniteScroll>
        <BackTop style={{ right: 10 }} />
        </div>
    </>
}

export default Index;